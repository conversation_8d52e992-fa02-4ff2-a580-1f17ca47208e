# 🤖 AI Assistant Cross-Platform Development Rules
## DassoShu Reader - Automatic Platform Awareness

> **CRITICAL**: These rules ensure AI assistants automatically follow cross-platform best practices when implementing ANY new feature, UI element, or functionality in DassoShu Reader.

---

## 🚨 **MANDATORY PATTERNS** (Always Use)

### **1. Icons - Semantic-First Material Design Strategy**
```dart
// ✅ ALWAYS USE - Semantic-first Material Design
Icon(AdaptiveIcons.home)
Icon(AdaptiveIcons.search)
Icon(AdaptiveIcons.settings)
Icon(AdaptiveIcons.back)

// ❌ NEVER USE - Platform-specific or hardcoded
Icon(Icons.home)           // Causes iOS question marks
Icon(CupertinoIcons.home)  // Android inconsistency
```

### **2. Design System - Zero Hardcoded Values**
```dart
// ✅ ALWAYS USE - DesignSystem constants
padding: EdgeInsets.all(DesignSystem.spaceM)
borderRadius: BorderRadius.circular(DesignSystem.radiusM)
elevation: DesignSystem.elevationS

// ❌ NEVER USE - Hardcoded values
padding: EdgeInsets.all(16.0)
borderRadius: BorderRadius.circular(8.0)
elevation: 4.0
```

### **3. Platform Checks - Use Adaptive Patterns**
```dart
// ✅ ALWAYS USE - Adaptive platform checks
if (PlatformAdaptations.isIOS) { ... }
if (PlatformAdaptations.isAndroid) { ... }

// ❌ NEVER USE - Direct platform checks
if (Platform.isIOS) { ... }
if (defaultTargetPlatform == TargetPlatform.iOS) { ... }
```

### **4. Navigation - Adaptive Routes**
```dart
// ✅ ALWAYS USE - Adaptive navigation
AdaptiveNavigation.push(context, MyPage());

// ❌ NEVER USE - Platform-specific routes
Navigator.push(context, MaterialPageRoute(...));
Navigator.push(context, CupertinoPageRoute(...));
```

---

## 🎯 **IMPLEMENTATION CHECKLIST**

When implementing ANY new feature, AI assistants must:

### **Before Writing Code:**
- [ ] Check if AdaptiveIcons exists for needed icons
- [ ] Verify DesignSystem constants for spacing/sizing
- [ ] Confirm PlatformAdaptations usage for platform logic
- [ ] Plan responsive design with ResponsiveSystem

### **During Implementation:**
- [ ] Use semantic-first Material Design for ALL icons
- [ ] Apply DesignSystem constants for ALL spacing/sizing
- [ ] Implement platform-adaptive behaviors via PlatformAdaptations
- [ ] Ensure 44dp minimum touch targets (DesignSystem.touchTargetSize)

### **After Implementation:**
- [ ] Test on both Android and iOS mentally
- [ ] Verify no hardcoded values exist
- [ ] Confirm accessibility compliance
- [ ] Check responsive behavior across screen sizes

---

## 📱 **PLATFORM-SPECIFIC ADAPTATIONS**

### **iOS Adaptations**
```dart
// Button styles
PlatformAdaptations.getAdaptiveButtonStyle(context)

// Scroll physics
PlatformAdaptations.adaptiveScrollPhysics

// Border radius
PlatformAdaptations.adaptiveBorderRadius
```

### **Android Adaptations**
```dart
// Material elevation
PlatformAdaptations.adaptiveElevation

// App bar height
PlatformAdaptations.adaptiveAppBarHeight
```

---

## 🚫 **COMMON VIOLATIONS TO AVOID**

### **Icons Violations**
```dart
// ❌ These cause iOS question marks
Icons.person → AdaptiveIcons.person
Icons.search → AdaptiveIcons.search
CupertinoIcons.settings → AdaptiveIcons.settings
```

### **Design System Violations**
```dart
// ❌ Hardcoded values
EdgeInsets.all(16.0) → EdgeInsets.all(DesignSystem.spaceM)
SizedBox(height: 24.0) → SizedBox(height: DesignSystem.spaceL)
```

### **Platform Check Violations**
```dart
// ❌ Direct platform checks
Platform.isIOS → PlatformAdaptations.isIOS
```

---

## 🔧 **ESSENTIAL IMPORTS**

Always include these imports for cross-platform development:
```dart
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/responsive_system.dart';
```

---

## 🎨 **UI COMPONENT PATTERNS**

### **Buttons**
```dart
ElevatedButton(
  style: PlatformAdaptations.getAdaptiveButtonStyle(context),
  child: Text('Button'),
  onPressed: () {},
)
```

### **Cards**
```dart
Card(
  elevation: PlatformAdaptations.adaptiveElevation,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(PlatformAdaptations.adaptiveBorderRadius),
  ),
  child: Padding(
    padding: EdgeInsets.all(DesignSystem.spaceM),
    child: content,
  ),
)
```

### **Lists**
```dart
ListTile(
  leading: Icon(AdaptiveIcons.iconName),
  title: Text('Title'),
  minVerticalPadding: DesignSystem.spaceS,
  contentPadding: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  ),
)
```

---

## 🚀 **QUICK DECISION TREE**

**Need an icon?** → Use `AdaptiveIcons.iconName`
**Need spacing?** → Use `DesignSystem.spaceXS/S/M/L/XL`
**Need platform check?** → Use `PlatformAdaptations.isIOS/isAndroid`
**Need navigation?** → Use `AdaptiveNavigation.push()`
**Need responsive design?** → Use `ResponsiveSystem.getScreenType()`

---

## ✅ **SUCCESS CRITERIA**

AI assistant implementations are successful when:
- ✅ Zero iOS question mark icons
- ✅ Zero hardcoded spacing/sizing values
- ✅ Consistent behavior across Android/iOS
- ✅ Proper accessibility compliance
- ✅ Responsive design across screen sizes

---

**Remember**: These patterns are proven successful in DassoShu Reader's 456/631 resolved cross-platform issues (72.3% complete). Follow them automatically for seamless development!

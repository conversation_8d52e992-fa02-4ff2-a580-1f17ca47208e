# 🤖 AI Assistant Cross-Platform Rules
## DassoShu Reader - Automatic Platform Awareness

> **For AI Assistants**: When implementing ANY new feature in DassoShu Reader, automatically follow these patterns to ensure Android/iOS compatibility.

---

## 🚨 **MANDATORY PATTERNS** (Always Use)

### **1. Icons → AdaptiveIcons (Semantic-First Material Design)**
```dart
// ✅ ALWAYS USE
Icon(AdaptiveIcons.home)
Icon(AdaptiveIcons.search)
Icon(AdaptiveIcons.settings)
Icon(AdaptiveIcons.person)

// ❌ NEVER USE (causes iOS question marks)
Icon(Icons.home)
Icon(CupertinoIcons.home)
```

### **2. Spacing → DesignSystem Constants**
```dart
// ✅ ALWAYS USE
padding: EdgeInsets.all(DesignSystem.spaceM)
SizedBox(height: DesignSystem.spaceL)
borderRadius: BorderRadius.circular(DesignSystem.radiusM)

// ❌ NEVER USE (hardcoded values)
padding: EdgeInsets.all(16.0)
SizedBox(height: 24.0)
```

### **3. Platform Checks → PlatformAdaptations**
```dart
// ✅ ALWAYS USE
if (PlatformAdaptations.isIOS) { ... }

// ❌ NEVER USE
if (Platform.isIOS) { ... }
```

### **4. Navigation → AdaptiveNavigation**
```dart
// ✅ ALWAYS USE
AdaptiveNavigation.push(context, MyPage());

// ❌ NEVER USE
Navigator.push(context, MaterialPageRoute(...));
```

---

## 🔧 **ESSENTIAL IMPORTS**
```dart
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
```

---

## 🚀 **VALIDATION** (Use Our Existing Tools)

Run during development:
```bash
# Real-time validation (already works!)
dart scripts/dev_validation.dart --watch

# Full analysis (already works!)
dart scripts/cross_platform_analyzer.dart --verbose
```

---

## ✅ **SUCCESS CRITERIA**
- ✅ Zero iOS question mark icons
- ✅ Zero hardcoded values
- ✅ Consistent Android/iOS behavior

---

**Note**: These patterns are proven in DassoShu Reader's cross-platform development. Our existing validation scripts catch violations automatically.

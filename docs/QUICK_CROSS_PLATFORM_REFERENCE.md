# ⚡ Quick Cross-Platform Reference
## Dasso<PERSON>hu Reader - Instant AI Assistant Guide

> **For AI Assistants**: Use this as your instant reference when implementing ANY feature in DassoShu Reader

---

## 🚨 **INSTANT FIXES** (Copy-Paste Ready)

### **Icons → AdaptiveIcons**
```dart
// ❌ WRONG → ✅ CORRECT
Icons.home → AdaptiveIcons.home
Icons.search → AdaptiveIcons.search  
Icons.settings → AdaptiveIcons.settings
Icons.person → AdaptiveIcons.person
Icons.back → AdaptiveIcons.back
Icons.close → AdaptiveIcons.close
Icons.add → AdaptiveIcons.add
Icons.edit → AdaptiveIcons.edit
Icons.delete → AdaptiveIcons.delete
Icons.share → AdaptiveIcons.share
Icons.favorite → AdaptiveIcons.favorite
Icons.bookmark → AdaptiveIcons.bookmark
Icons.play_arrow → AdaptiveIcons.play
Icons.pause → AdaptiveIcons.pause
Icons.stop → AdaptiveIcons.stop
Icons.volume_up → AdaptiveIcons.volumeUp
Icons.volume_off → AdaptiveIcons.volumeOff
Icons.brightness_high → AdaptiveIcons.brightnessHigh
Icons.brightness_low → AdaptiveIcons.brightnessLow
Icons.translate → AdaptiveIcons.translate
Icons.book → AdaptiveIcons.book
Icons.library_books → AdaptiveIcons.library
Icons.school → AdaptiveIcons.school
Icons.quiz → AdaptiveIcons.quiz
Icons.note → AdaptiveIcons.note
Icons.folder → AdaptiveIcons.folder
Icons.download → AdaptiveIcons.download
Icons.upload → AdaptiveIcons.upload
Icons.sync → AdaptiveIcons.sync
Icons.refresh → AdaptiveIcons.refresh
Icons.info → AdaptiveIcons.info
Icons.help → AdaptiveIcons.help
Icons.bug_report → AdaptiveIcons.bugReport
Icons.style → AdaptiveIcons.style
```

### **Spacing → DesignSystem**
```dart
// ❌ WRONG → ✅ CORRECT
EdgeInsets.all(4.0) → EdgeInsets.all(DesignSystem.spaceXS)
EdgeInsets.all(8.0) → EdgeInsets.all(DesignSystem.spaceS)
EdgeInsets.all(16.0) → EdgeInsets.all(DesignSystem.spaceM)
EdgeInsets.all(24.0) → EdgeInsets.all(DesignSystem.spaceL)
EdgeInsets.all(32.0) → EdgeInsets.all(DesignSystem.spaceXL)

SizedBox(height: 8.0) → SizedBox(height: DesignSystem.spaceS)
SizedBox(height: 16.0) → SizedBox(height: DesignSystem.spaceM)
SizedBox(height: 24.0) → SizedBox(height: DesignSystem.spaceL)

BorderRadius.circular(4.0) → BorderRadius.circular(DesignSystem.radiusS)
BorderRadius.circular(8.0) → BorderRadius.circular(DesignSystem.radiusM)
BorderRadius.circular(16.0) → BorderRadius.circular(DesignSystem.radiusL)
```

### **Platform Checks → PlatformAdaptations**
```dart
// ❌ WRONG → ✅ CORRECT
Platform.isIOS → PlatformAdaptations.isIOS
Platform.isAndroid → PlatformAdaptations.isAndroid
defaultTargetPlatform == TargetPlatform.iOS → PlatformAdaptations.isIOS
```

---

## 🎯 **COMMON PATTERNS** (Ready to Use)

### **Button with Adaptive Styling**
```dart
ElevatedButton(
  style: PlatformAdaptations.getAdaptiveButtonStyle(context),
  onPressed: () { /* action */ },
  child: Text('Button'),
)
```

### **Card with Platform Styling**
```dart
Card(
  elevation: PlatformAdaptations.adaptiveElevation,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(PlatformAdaptations.adaptiveBorderRadius),
  ),
  child: Padding(
    padding: EdgeInsets.all(DesignSystem.spaceM),
    child: content,
  ),
)
```

### **ListTile with Adaptive Icon**
```dart
ListTile(
  leading: Icon(AdaptiveIcons.iconName),
  title: Text('Title'),
  minVerticalPadding: DesignSystem.spaceS,
  contentPadding: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  ),
  onTap: () { /* action */ },
)
```

### **AppBar with Adaptive Height**
```dart
AppBar(
  title: Text('Title'),
  leading: IconButton(
    icon: Icon(AdaptiveIcons.back),
    onPressed: () => Navigator.of(context).pop(),
  ),
  toolbarHeight: PlatformAdaptations.adaptiveAppBarHeight,
)
```

---

## 🔧 **ESSENTIAL IMPORTS** (Always Include)
```dart
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/responsive_system.dart';
```

---

## ⚡ **VS CODE SHORTCUTS**

Type these prefixes in VS Code for instant snippets:
- `dsicon` → Adaptive icon
- `dspadding` → Design System padding
- `dsplatform` → Platform check
- `dsnav` → Adaptive navigation
- `dsbutton` → Adaptive button
- `dscard` → Adaptive card
- `dsimports` → Essential imports

---

## 🚫 **NEVER USE** (Causes iOS Issues)

```dart
// These cause iOS question mark icons:
Icons.* (use AdaptiveIcons.* instead)
CupertinoIcons.* (use AdaptiveIcons.* instead)

// These cause inconsistent spacing:
EdgeInsets.all(16.0) (use DesignSystem.spaceM)
SizedBox(height: 24.0) (use DesignSystem.spaceL)

// These cause platform issues:
Platform.isIOS (use PlatformAdaptations.isIOS)
MaterialPageRoute (use AdaptiveNavigation.push)
```

---

## ✅ **VALIDATION COMMANDS**

Run these in terminal for instant feedback:
```bash
# Watch mode (recommended during development)
dart scripts/dev_validation.dart --watch

# Full analysis
dart scripts/cross_platform_analyzer.dart --verbose

# Custom lint
dart run custom_lint
```

---

## 🎯 **SUCCESS CHECKLIST**

Before submitting code, ensure:
- [ ] All icons use `AdaptiveIcons.*`
- [ ] All spacing uses `DesignSystem.*`
- [ ] All platform checks use `PlatformAdaptations.*`
- [ ] No hardcoded values exist
- [ ] Essential imports included

---

**Remember**: These patterns eliminate iOS question marks, ensure Android/iOS consistency, and maintain professional code quality. Use them automatically!
